import java.util.Arrays;

public class Array {
    // 1)
    public int[] systemArrayCopy(int[] nums) {
        int[] array1 = new int[nums.length];
        System.arraycopy(nums, 0, array1, 0, nums.length);
        return array1;
    }

    /**
     * public static void arraycopy(Object src, int srcPos, Object dest, int
     * destPos, int length)
     * src:源数组;
     * srcPos:源数组要复制的起始位置;
     * dest:目的数组;
     * destPos:目的数组放置的起始位置;
     * length:复制的长度.
     */
    public int[] rangeArrayCopy(int[] nums) {
        int[] array2 = Arrays.copyOfRange(nums, 0, nums.length);
        return array2;
    }

    // 默认从 0 开始复制
    public int[] ofArrayCopy(int[] nums) {
        int[] array3 = Arrays.copyOf(nums, nums.length);
        return array3;
    }

    // 2)
    public void toStringPrint(int[] nums) {
        System.out.println(Arrays.toString(nums));
    }

    public void printArrays(int[] nums) {
        for (int i = 0; i < nums.length; i++) {
            System.out.print(nums[i] + " ");
            System.out.printf("/n");
        }
    }

    // 3)
    public int[] creatRandomArrays(int[] nums) {
        int[] array4 = new int[101];
        for (int i = 0; i < array4.length; i++) {
            array4[i] = (int) (Math.random() * 100);
        }
        return array4;
    }

    public int[] sortArrays(int[] nums) {
        int[] array5 = Arrays.copyOf(nums, nums.length);
        Arrays.sort(array5);
        return array5;
    }

    public int[] selectSortArrays(int[] nums) {
        int[] array5 = Arrays.copyOf(nums, nums.length);
        for (int i = 0; i < array5.length - 1; i++) {
            int minIndex = i;
            for (int j = i + 1; j < array5.length; j++) {
                if (array5[j] < array5[minIndex]) {
                    minIndex = j;
                }
            }
            int temp = array5[minIndex];
            array5[minIndex] = array5[i];
            array5[i] = temp;
        }
        return array5;
    }

    public int[] bubbleSortArrays(int nums[]) {
        int[] array6 = Arrays.copyOf(nums, nums.length);
        for (int i = 0; i < array6.length; i++) {
            int flag = 0;
            if (array6[i] > array6[i + 1]) {
                int temp = array6[i + 1];
                array6[i + 1] = array6[i];
                array6[i] = temp;
                flag = 1;
            }
            if (flag == 0) {
                break;
            }
        }
        return array6;
    }

    // 4)
    public int directTraversalArrays(int nums[], int index) {
        for (int i = 0; i < nums.length; i++) {
            if (nums[i] == index) {
                return i;
            }
        }
        return -1;
    }

    public int binarySearchArrays(int[] nums, int index) {
        int[] array7 = sortArrays(nums);// 排序
        int left = 0, right = array7.length - 1;
        while (left <= right) {
            int mid = (left + right) / 2;
            if (mid < index) {
                left = mid + 1;
            } else if (mid > index) {
                right = mid - 1;
            } else
                return mid;
        }
        return -1;
    }

    // 5)
    public boolean isSameArrays(int[] nums1, int[] nums2) {
        if (nums1.length != nums2.length)
            return false;
        for (int i = 0; i < nums1.length; i++) {
            if (nums1[i] != nums2[i])
                return false;
        }
        return true;
    }

    // 6)
    public void fillArrays(int[] nums, int index) {
        Arrays.fill(null, nums);
    }

    // 7)
    public void twoD_bubbleSort() {
        int[][] array8 = new int[11][11];
        for (int i = 0; i < array8.length; i++) {
            for (int j = 0; j < array8[0].length; j++) {
                array8[i][j] = (int) (Math.random() * 100);
            }
        }
        for (int i = 0; i < array8.length; i++) {
            bubbleSortArrays(array8[i]);
        }
        // sort方法是对一维数组进行排序，无法直接对二维数组使用
    }

    public void twoD_Sort() {
        int[][] array8 = new int[11][11];
        for (int i = 0; i < array8.length; i++) {
            for (int j = 0; j < array8[0].length; j++) {
                array8[i][j] = (int) (Math.random() * 100);
            }
        }
        int[] temp = new int[101];
        int destPos = 0;
        for (int i = 0; i < array8.length; i++) {
            System.arraycopy(array8[i], 0, temp, destPos, array8[i].length);
            destPos += array8[i].length;
        }
        Arrays.sort(temp);
        int Pos = 0;
        for (int i = 0; i < array8.length; i++) {
            for (int j = 0; j < array8[0].length; j++) {
                array8[i][j] = temp[Pos];
                Pos++;
            }
        }
    }
}
