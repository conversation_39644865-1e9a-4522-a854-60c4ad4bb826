import java.util.*;

/**
 * 公司人事薪资计算系统 - 使用工厂模式和策略模式
 */

// 薪资计算策略接口
interface SalaryCalculator {
    double computePay(double... params);
}

// 人员信息接口
interface PersonInfo {
    String getFirstName();

    String getLastName();

    String getSSN();

    void setFirstName(String name);

    void setLastName(String name);
}

// 基础人员信息实现
class BasicPersonInfo implements PersonInfo {
    private String firstName;
    private String lastName;
    private String ssn;

    public BasicPersonInfo(String firstName, String lastName, String ssn) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.ssn = ssn;
    }

    public String getFirstName() {
        return firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public String getSSN() {
        return ssn;
    }

    public void setFirstName(String name) {
        this.firstName = name;
    }

    public void setLastName(String name) {
        this.lastName = name;
    }
}

// 薪资计算器工厂
class PayrollCalculatorFactory {
    public static SalaryCalculator createCalculator(int type) {
        switch (type) {
            case 0:
                return new WeeklyPayCalculator();
            case 1:
                return new HourlyPayCalculator();
            case 2:
                return new CommissionPayCalculator();
            case 3:
                return new BaseCommissionPayCalculator();
            default:
                throw new IllegalArgumentException("Unknown calculator type");
        }
    }
}

// 具体薪资计算策略
class WeeklyPayCalculator implements SalaryCalculator {
    public double computePay(double... params) {
        return params[0] * 4; // 周薪 * 4
    }
}

class HourlyPayCalculator implements SalaryCalculator {
    public double computePay(double... params) {
        return params[0] * params[1]; // 时薪 * 小时数
    }
}

class CommissionPayCalculator implements SalaryCalculator {
    public double computePay(double... params) {
        return params[0] * params[1]; // 销售额 * 提成率
    }
}

class BaseCommissionPayCalculator implements SalaryCalculator {
    public double computePay(double... params) {
        return params[0] * params[1] + params[2]; // 销售额 * 提成率 + 底薪
    }
}

// 员工记录类 - 使用组合模式
class EmployeeRecord implements Comparable<EmployeeRecord> {
    private PersonInfo personalInfo;
    private SalaryCalculator payCalculator;
    private double[] salaryParams;

    public EmployeeRecord(PersonInfo info, SalaryCalculator calculator, double[] params) {
        this.personalInfo = info;
        this.payCalculator = calculator;
        this.salaryParams = params.clone();
    }

    public String getFirstName() {
        return personalInfo.getFirstName();
    }

    public String getLastName() {
        return personalInfo.getLastName();
    }

    public String getSSN() {
        return personalInfo.getSSN();
    }

    public double calculateSalary() {
        return payCalculator.computePay(salaryParams);
    }

    @Override
    public String toString() {
        return String.format("firstName:%s; lastName:%s; socialSecurityNumber:%s; earning:%.2f",
                getFirstName(), getLastName(), getSSN(), calculateSalary());
    }

    @Override
    public int compareTo(EmployeeRecord other) {
        return Double.compare(this.calculateSalary(), other.calculateSalary());
    }
}

// 员工数据库管理器
class EmployeeDatabase {
    private List<EmployeeRecord> employees;

    public EmployeeDatabase() {
        this.employees = new ArrayList<>();
    }

    public void addEmployee(EmployeeRecord employee) {
        employees.add(employee);
    }

    public List<EmployeeRecord> searchByFirstName(String firstName) {
        List<EmployeeRecord> results = new ArrayList<>();
        for (EmployeeRecord emp : employees) {
            if (emp.getFirstName().equals(firstName)) {
                results.add(emp);
            }
        }
        Collections.sort(results);
        return results;
    }

    public List<EmployeeRecord> searchBySSN(String ssn) {
        List<EmployeeRecord> results = new ArrayList<>();
        for (EmployeeRecord emp : employees) {
            if (emp.getSSN().equals(ssn)) {
                results.add(emp);
            }
        }
        Collections.sort(results);
        return results;
    }
}

public class Main {
    public static void main(String[] args) {
        Scanner input = new Scanner(System.in);
        EmployeeDatabase database = new EmployeeDatabase();

        // 读取员工数量
        int employeeCount = input.nextInt();

        // 读取员工信息并创建记录
        for (int i = 0; i < employeeCount; i++) {
            int payrollType = input.nextInt();
            String firstName = input.next();
            String lastName = input.next();
            String ssn = input.next();

            // 创建人员信息对象
            PersonInfo personInfo = new BasicPersonInfo(firstName, lastName, ssn);

            // 根据类型创建薪资计算器和参数
            SalaryCalculator calculator = PayrollCalculatorFactory.createCalculator(payrollType);
            double[] params;

            switch (payrollType) {
                case 0: // 周薪员工
                    double weeklySalary = input.nextDouble();
                    params = new double[] { weeklySalary };
                    break;
                case 1: // 小时工
                    double hourlyWage = input.nextDouble();
                    double hours = input.nextDouble();
                    params = new double[] { hourlyWage, hours };
                    break;
                case 2: // 提成员工
                    double sales = input.nextDouble();
                    double commission = input.nextDouble();
                    params = new double[] { sales, commission };
                    break;
                case 3: // 底薪加提成员工
                    double salesAmount = input.nextDouble();
                    double commissionRate = input.nextDouble();
                    double basePay = input.nextDouble();
                    params = new double[] { salesAmount, commissionRate, basePay };
                    break;
                default:
                    params = new double[0];
            }

            // 创建员工记录并添加到数据库
            EmployeeRecord record = new EmployeeRecord(personInfo, calculator, params);
            database.addEmployee(record);
        }

        // 处理查询
        int queryCount = input.nextInt();
        for (int i = 0; i < queryCount; i++) {
            int queryType = input.nextInt();
            String queryValue = input.next();

            List<EmployeeRecord> results;
            if (queryType == 0) {
                results = database.searchByFirstName(queryValue);
            } else {
                results = database.searchBySSN(queryValue);
            }

            // 输出结果
            for (EmployeeRecord record : results) {
                System.out.println(record.toString());
            }
        }

        input.close();
    }
}
