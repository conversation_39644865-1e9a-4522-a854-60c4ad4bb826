import java.util.*;

/**
 * 集合运算类，实现集合的交集、并集和余集运算
 */
public class SetOperations {
    
    /**
     * 计算两个集合的交集
     * @param setA 集合A
     * @param setB 集合B
     * @return 交集
     */
    public static Set<Integer> intersection(Set<Integer> setA, Set<Integer> setB) {
        Set<Integer> result = new TreeSet<>();
        for (Integer element : setA) {
            if (setB.contains(element)) {
                result.add(element);
            }
        }
        return result;
    }
    
    /**
     * 计算两个集合的并集
     * @param setA 集合A
     * @param setB 集合B
     * @return 并集
     */
    public static Set<Integer> union(Set<Integer> setA, Set<Integer> setB) {
        Set<Integer> result = new TreeSet<>();
        result.addAll(setA);
        result.addAll(setB);
        return result;
    }
    
    /**
     * 计算B在A中的余集（A - B）
     * @param setA 集合A
     * @param setB 集合B
     * @return A中不在B中的元素
     */
    public static Set<Integer> complement(Set<Integer> setA, Set<Integer> setB) {
        Set<Integer> result = new TreeSet<>();
        for (Integer element : setA) {
            if (!setB.contains(element)) {
                result.add(element);
            }
        }
        return result;
    }
    
    /**
     * 输出集合中的元素，用空格分隔
     * @param set 要输出的集合
     */
    public static void printSet(Set<Integer> set) {
        if (set.isEmpty()) {
            System.out.println();
            return;
        }
        
        boolean first = true;
        for (Integer element : set) {
            if (!first) {
                System.out.print(" ");
            }
            System.out.print(element);
            first = false;
        }
        System.out.println();
    }
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        // 读取集合A
        int n = scanner.nextInt();
        Set<Integer> setA = new TreeSet<>();
        for (int i = 0; i < n; i++) {
            setA.add(scanner.nextInt());
        }
        
        // 读取集合B
        int m = scanner.nextInt();
        Set<Integer> setB = new TreeSet<>();
        for (int i = 0; i < m; i++) {
            setB.add(scanner.nextInt());
        }
        
        // 计算并输出交集
        Set<Integer> intersectionResult = intersection(setA, setB);
        printSet(intersectionResult);
        
        // 计算并输出并集
        Set<Integer> unionResult = union(setA, setB);
        printSet(unionResult);
        
        // 计算并输出B在A中的余集（A - B）
        Set<Integer> complementResult = complement(setA, setB);
        printSet(complementResult);
        
        scanner.close();
    }
}
