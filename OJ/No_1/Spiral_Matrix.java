//!格式
//!错误检测以及输出

import java.util.Scanner;
import java.util.InputMismatchException;

public class Spiral_Matrix {
    public static void main(String[] args) {
        int n = 0;
        boolean validInput = false;
        Scanner scanner = new Scanner(System.in);
        while (!validInput) {
            try {
                n = scanner.nextInt();
                if (n <= 0 || n > 50) {
                    System.out.println("Input data error");
                    return; // 非合法整数
                }
                validInput = true;
            } catch (InputMismatchException e) {
                System.out.println("Input data error");
                return;// 错误输入
            }
        }
        scanner.close();
        int[][] arr = new int[n][n];
        Spiral_Matrix spiral_Matrix = new Spiral_Matrix();
        spiral_Matrix.Solution(n, arr);
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (j == 0)
                    System.out.print(arr[i][j]);
                else
                    System.out.print(" " + arr[i][j]);
            }
            System.out.println();
        }
    }

    public int[][] Solution(int n, int[][] nums) {
        int startx, starty;
        int loop = n / 2;
        int offset = 0;
        int count = 1;
        // 特殊处理奇数 n 的中心
        if (n % 2 == 1) {
            int mid = n / 2;
            nums[mid][mid] = n * n;
        }
        while (loop > 0) {
            startx = offset;
            starty = n - offset - 1;
            int x = startx;
            int y = starty;
            for (int i = x; i < n - offset - 1; i++) {
                nums[x][y] = count++;
                x++;
            }
            for (int i = y; i > offset; i--) {
                nums[x][y] = count++;
                y--;
            }
            for (int i = x; i > offset; i--) {
                nums[x][y] = count++;
                x--;
            }
            for (int i = y; i < n - offset - 1; i++) {
                nums[x][y] = count++;
                y++;
            }
            offset++;
            loop--;
        }
        return nums;
    }
}
