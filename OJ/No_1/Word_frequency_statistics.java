import java.util.Scanner;

public class Word_frequency_statistics {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        int n, m;
        n = scanner.nextInt();
        m = scanner.nextInt();
        int[] includes = new int[m];
        int[] sum = new int[m];
        // 对每个文章

        for (int i = 0; i < n; i++) {
            int length = scanner.nextInt();
            int[] arr = new int[length];
            // 输入文章内容
            for (int j = 0; j < length; j++) {
                arr[j] = scanner.nextInt();
            }
            // 初始化检测是否包含过数组
            boolean[] isIncluded = new boolean[m];
            for (int j = 0; j < m; j++) {
                isIncluded[j] = false;
            }
            // 更新答案数组
            for (int j = 0; j < length; j++) {
                sum[arr[j] - 1]++;
                if (!isIncluded[arr[j] - 1]) {
                    includes[arr[j] - 1]++;
                    isIncluded[arr[j] - 1] = true;
                }
            }
        }
        for (int i = 0; i < m; i++) {
            System.out.println(includes[i] + " " + sum[i]);
        }
        scanner.close();
    }

}
