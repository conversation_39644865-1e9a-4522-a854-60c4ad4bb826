
//!格式
import java.util.ArrayList;
import java.util.Scanner;
import java.util.List;

public class Student_Info_Management {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        int n = scanner.nextInt();
        for (int i = 0; i < n; i++) {
            int m = scanner.nextInt();
            if (m == 1) {
                String id = scanner.next();
                String name = scanner.next();
                int math = scanner.nextInt();
                int english = scanner.nextInt();
                int java = scanner.nextInt();
                StudentManager.addStudent(id, name, math, english, java);
            } else if (m == 2) {
                String id = scanner.next();
                StudentManager.deleteStudent(id);
            } else if (m == 3) {
                String id = scanner.next();
                int math = scanner.nextInt();
                int english = scanner.nextInt();
                int java = scanner.nextInt();
                StudentManager.updateStudent(id, math, english, java);
            } else if (m == 4) {
                String id = scanner.next();
                StudentManager.showStudentGrades(id);
            }
        }
        scanner.close();
    }

    public static class Student {
        private String id;
        private String name;
        private int math;
        private int english;
        private int java;

        public Student(String id, String name, int math, int english, int java) {
            this.id = id;
            this.name = name;
            this.math = math;
            this.english = english;
            this.java = java;
        }

        public String getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        public int getMath() {
            return math;
        }

        public void setMath(int math) {
            this.math = math;
        }

        public void setEnglish(int english) {
            this.english = english;
        }

        public void setJava(int java) {
            this.java = java;
        }

        public double getAverageScore() {
            return (math + english + java) / 3.0;
        }
    }

    public static class StudentManager {
        private static List<Student> students = new ArrayList<>();

        public static boolean addStudent(String id, String name, int math, int english, int java) {
            if (students.stream().anyMatch(student -> student.getId().equals(id))) {
                System.out.println("Students already exist");
                return false;
            } else {
                students.add(new Student(id, name, math, english, java));
                System.out.println("Add success");
                return true;
            }
        }

        public static boolean deleteStudent(String id) {
            boolean isDeleted = students.removeIf(student -> student.getId().equals(id));
            // !
            if (isDeleted) {
                System.out.println("Delete success");
                return true;
            } else {
                System.out.println("Students do not exist");
                return false;
            }
        }

        public static boolean updateStudent(String id, int math, int english, int java) {

            Student student = students.stream()
                    .filter(s -> s.getId().equals(id))
                    .findFirst()
                    .orElse(null);
            // !重要
            if (student != null) {
                student.setMath(math);
                student.setEnglish(english);
                student.setJava(java);
                System.out.println("Update success");
                return true;
            }
            System.out.println("Students do not exist");
            return false;
        }

        public static boolean showStudentGrades(String id) {
            Student student = students.stream()
                    .filter(s -> s.getId().equals(id))
                    .findFirst()
                    .orElse(null);
            if (student != null) {
                System.out.printf("Student ID:%s\nName:%s\nAverage Score:%.1f\n", student.getId(), student.getName(),
                        student.getAverageScore());
                return true;
            } else {
                System.out.println("Students do not exist");
                return false;
            }
        }

    }
}
